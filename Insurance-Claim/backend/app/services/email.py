import boto3
import logging
import os
from dotenv import load_dotenv

load_dotenv()

logger = logging.getLogger(__name__)

def send_verification_email(to_email: str, verify_link: str):
    # Check if AWS credentials are configured
    if not os.getenv('AWS_SES_ACCESS_KEY') or not os.getenv('AWS_SES_SECRET_KEY'):
        logger.warning("AWS SES credentials not configured. Skipping email send.")
        logger.info(f"Verification link for {to_email}: {verify_link}")
        return {"MessageId": "dev-mode"}
    
    try:
        client = boto3.client(
            'ses',
            region_name=os.getenv('AWS_SES_REGION'),
            aws_access_key_id=os.getenv('AWS_SES_ACCESS_KEY'),
            aws_secret_access_key=os.getenv('AWS_SES_SECRET_KEY')
        )
        subject = 'Verify your email'
        body = f'Click the link to verify your email: {verify_link}'
        response = client.send_email(
            Source=os.getenv('EMAIL_FROM'),
            Destination={'ToAddresses': [to_email]},
            Message={
                'Subject': {'Data': subject},
                'Body': {'Text': {'Data': body}}
            }
        )
        return response
    except Exception as e:
        logger.error(f"Failed to send email via AWS SES: {e}")
        logger.info(f"Verification link for {to_email}: {verify_link}")
        return {"MessageId": "error-fallback"} 