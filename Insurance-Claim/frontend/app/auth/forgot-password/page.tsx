'use client';

import { useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Alert } from '@/components/ui/alert';
import { Label } from '@/components/ui/label';
import { 
  Mail, 
  Shield, 
  ArrowLeft, 
  CheckCircle, 
  AlertCircle,
  Send
} from 'lucide-react';
import { FadeIn, SlideIn, LoadingButton } from '@/components/ui/loading';

export default function ForgotPassword() {
  const [email, setEmail] = useState('');
  const [message, setMessage] = useState('');
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isEmailSent, setIsEmailSent] = useState(false);
  const router = useRouter();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setMessage('');
    setIsLoading(true);

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      const res = await fetch(process.env.NEXT_PUBLIC_API_URL + '/auth/forgot-password', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ email })
      });
      
      const data = await res.json();
      if (!res.ok) throw new Error(data.detail || 'Failed to send reset email');
      
      setMessage('Password reset email sent successfully! Please check your inbox.');
      setIsEmailSent(true);
      
    } catch (err: any) {
      setError(err.message || 'Failed to send reset email. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="w-full max-w-md mx-auto">
      {/* Header */}
      <FadeIn delay={100}>
        <div className="text-center mb-8">
          <div className="flex justify-center mb-4">
            <div className="p-3 bg-blue-100 rounded-full">
              <Shield className="h-8 w-8 text-blue-600" />
            </div>
          </div>
          <h2 className="text-3xl font-bold text-gray-900 mb-2">
            {isEmailSent ? 'Check Your Email' : 'Reset Password'}
          </h2>
          <p className="text-gray-600">
            {isEmailSent 
              ? 'We\'ve sent password reset instructions to your email address.'
              : 'Enter your email address and we\'ll send you a link to reset your password.'
            }
          </p>
        </div>
      </FadeIn>

      {!isEmailSent ? (
        <>
          {/* Reset Form */}
          <SlideIn direction="up" delay={200}>
            <form onSubmit={handleSubmit} className="space-y-6">
              <div>
                <Label htmlFor="email" className="text-sm font-medium text-gray-700">
                  Email Address
                </Label>
                <div className="relative mt-1">
                  <Mail className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
                  <Input
                    id="email"
                    type="email"
                    placeholder="Enter your email address"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    className="pl-10 hover-lift focus:scale-[1.02] transition-transform"
                    required
                  />
                </div>
              </div>

              <LoadingButton
                type="submit"
                loading={isLoading}
                className="w-full hover-lift group bg-blue-600 hover:bg-blue-700"
              >
                <Send className="h-4 w-4 mr-2 group-hover:scale-110 transition-transform" />
                {isLoading ? 'Sending...' : 'Send Reset Link'}
              </LoadingButton>
            </form>
          </SlideIn>

          {/* Back to Login */}
          <SlideIn direction="up" delay={300}>
            <div className="mt-6 text-center">
              <Link
                href="/auth/login"
                className="inline-flex items-center text-sm text-blue-600 hover:text-blue-500 transition-colors"
              >
                <ArrowLeft className="h-4 w-4 mr-1" />
                Back to Login
              </Link>
            </div>
          </SlideIn>
        </>
      ) : (
        <>
          {/* Success State */}
          <SlideIn direction="up" delay={200}>
            <div className="bg-green-50 rounded-lg p-6 mb-6">
              <div className="text-center">
                <div className="inline-flex items-center justify-center w-16 h-16 bg-white rounded-full shadow-lg mb-4">
                  <Mail className="h-8 w-8 text-green-600" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Reset Link Sent</h3>
                <p className="text-sm text-gray-600">
                  Check your email inbox and spam folder for the password reset link.
                </p>
              </div>
            </div>
          </SlideIn>

          {/* Instructions */}
          <SlideIn direction="up" delay={300}>
            <div className="space-y-4 mb-6">
              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0 w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center mt-0.5">
                  <span className="text-xs font-bold text-blue-600">1</span>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-900">Check your email</p>
                  <p className="text-xs text-gray-600">Look for an email from Insurance Litigation System</p>
                </div>
              </div>
              
              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0 w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center mt-0.5">
                  <span className="text-xs font-bold text-blue-600">2</span>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-900">Click the reset link</p>
                  <p className="text-xs text-gray-600">This will take you to the password reset page</p>
                </div>
              </div>
              
              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0 w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center mt-0.5">
                  <span className="text-xs font-bold text-blue-600">3</span>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-900">Create a new password</p>
                  <p className="text-xs text-gray-600">Choose a strong, secure password</p>
                </div>
              </div>
            </div>
          </SlideIn>

          {/* Actions */}
          <SlideIn direction="up" delay={400}>
            <div className="space-y-3">
              <Button
                onClick={() => router.push('/auth/login')}
                className="w-full hover-lift bg-blue-600 hover:bg-blue-700"
              >
                Back to Login
              </Button>
              
              <Button
                onClick={() => {
                  setIsEmailSent(false);
                  setEmail('');
                  setMessage('');
                  setError('');
                }}
                variant="outline"
                className="w-full hover-lift"
              >
                Send Another Email
              </Button>
            </div>
          </SlideIn>
        </>
      )}

      {/* Messages */}
      {message && (
        <FadeIn delay={0}>
          <Alert className="mt-4 border-green-200 bg-green-50">
            <CheckCircle className="h-4 w-4 text-green-600" />
            <span className="text-green-800">{message}</span>
          </Alert>
        </FadeIn>
      )}

      {error && (
        <FadeIn delay={0}>
          <Alert className="mt-4" variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <span>{error}</span>
          </Alert>
        </FadeIn>
      )}

      {/* Help */}
      <SlideIn direction="up" delay={500}>
        <div className="mt-8 p-4 bg-gray-50 rounded-lg">
          <div className="text-center">
            <p className="text-sm text-gray-600 mb-2">
              Still having trouble?
            </p>
            <Link
              href="/support"
              className="text-sm text-blue-600 hover:text-blue-500 transition-colors"
            >
              Contact Support
            </Link>
          </div>
        </div>
      </SlideIn>

      {/* Security Notice */}
      <SlideIn direction="up" delay={600}>
        <div className="mt-4 p-4 bg-gray-50 rounded-lg">
          <div className="flex items-center text-sm text-gray-600">
            <Shield className="h-4 w-4 mr-2 text-green-600" />
            <span>Password reset links expire after 24 hours for security</span>
          </div>
        </div>
      </SlideIn>
    </div>
  );
}
