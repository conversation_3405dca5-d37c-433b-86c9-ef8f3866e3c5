import os
import sys
import time
from fastapi import HTTPException
from langchain_unstructured import UnstructuredLoader
from langchain_community.document_loaders import PyPDFLoader, UnstructuredWordDocumentLoader
from langchain_community.document_loaders.excel import UnstructuredExcelLoader
from langchain_openai import OpenAIEmbeddings
from pinecone import Pinecone
from langchain_core.utils.iter import batch_iterate
import nltk
import copy
import uuid
from dotenv import load_dotenv
from typing import List, Dict, Union, Any
import json


# Download required NLTK data
nltk.download('punkt')
nltk.download('punkt_tab')
nltk.download('averaged_perceptron_tagger_eng')
nltk.download('averaged_perceptron_tagger')

# Load environment variables
load_dotenv()

def get_environment_variables():
    """
    Get environment variables directly from .env file with validation.
    """
    openai_api_key = os.getenv('OPENAI_API_KEY')
    pinecone_key = os.getenv('PINECONE_API_KEY')
    pinecone_index = os.getenv('PINECONE_INDEX')

    # Validate environment variables
    if not all([openai_api_key, pinecone_key, pinecone_index]):
        missing_vars = []
        if not openai_api_key:
            missing_vars.append('OPENAI_API_KEY')
        if not pinecone_key:
            missing_vars.append('PINECONE_API_KEY')
        if not pinecone_index:
            missing_vars.append('PINECONE_INDEX')
        raise ValueError(f"Missing required environment variables: {', '.join(missing_vars)}")

    return openai_api_key, pinecone_key, pinecone_index


def clean_metadata_value(value: Any) -> Union[str, int, float, bool, List[str]]:
    """
    Clean metadata values to ensure they're Pinecone-compatible.
    Pinecone only accepts strings, numbers, booleans, or lists of strings.
    """
    if isinstance(value, (str, int, float, bool)):
        return value
    elif isinstance(value, list):
        return [str(item) for item in value]
    elif isinstance(value, dict):
        return json.dumps(value)
    else:
        return str(value)


def clean_metadata(metadata: Dict) -> Dict:
    """Clean metadata dictionary to ensure all values are Pinecone-compatible."""
    cleaned = {}
    for key, value in metadata.items():
        if key != 'coordinates':  # Skip coordinates field entirely
            cleaned[key] = clean_metadata_value(value)
    return cleaned


def string_size_in_kb(s: str) -> float:
    """Calculate the size of a string in kilobytes."""
    size_in_bytes = sys.getsizeof(s)
    return size_in_bytes / 1024


def split_text_by_kb(text: str, number: int) -> List[str]:
    """Split text into chunks based on the specified number of divisions."""
    chunks = []
    start = 0
    distance = int(len(text) / number) + 1

    while start < len(text):
        end = min(start + distance, len(text))
        chunk = text[start:end]
        chunks.append(chunk)
        start = end

    return chunks


async def process_and_train(file_path: str, namespace: str):
    """
    Process and train documents using Pinecone and OpenAI embeddings.
    """
    print("Start processing and training")
    try:
        # Get environment variables
        openai_api_key, pinecone_key, pinecone_index = get_environment_variables()
        
        if not os.path.exists(file_path):
            raise HTTPException(
                status_code=404, detail=f"File not found: {file_path}")
        print(
            f"Starting document processing and training for file: {file_path}")

        # Load the document
        try:
            if file_path.lower().endswith('.pdf'):
                print(file_path)
                loader = PyPDFLoader(file_path=file_path)
            
            elif file_path.lower().endswith('.docx'):
                print(file_path)
                loader = UnstructuredWordDocumentLoader(file_path=file_path)

            elif file_path.lower().endswith('.xlsx'):
                print(file_path)
                loader = UnstructuredExcelLoader(file_path=file_path)

            else:
                # Skip the file if it's not a PDF or DOCX
                print(f"Skipping file {file_path}, not a PDF or DOCX")
                return {
                    "status": "error",
                    "message": f"Unsupported file type: {file_path}",
                    "details": {
                        "file_name": os.path.basename(file_path),
                        "error": "File type not supported"
                    }
                }

            docs = loader.load() 
        except Exception as e:
            print(f"Failed to load {file_path}. Error: {e}")
            return {
                "status": "error",
                "message": f"Failed to load document: {str(e)}",
                "details": {
                    "file_name": os.path.basename(file_path),
                    "error": str(e)
                }
            }

        # Initialize OpenAI embeddings
        embeddings_model = OpenAIEmbeddings(openai_api_key=openai_api_key)

        # Initialize Pinecone
        pc = Pinecone(api_key=pinecone_key)
        existing_indexes = [index_info["name"]
                            for index_info in pc.list_indexes()]
        print(f"Existing Pinecone indexes: {existing_indexes}")

        index = pc.Index(existing_indexes[0])

        # Process documents
        texts = []
        metadatas = []

        # Split documents into appropriate chunks
        for doc in docs:
            encode_size = string_size_in_kb(doc.page_content)
            if encode_size > 40:
                divide_number = int(encode_size / 40) + 1
                print(divide_number)
                split_encoded_texts = split_text_by_kb(doc.page_content, divide_number)
                for chuck in split_encoded_texts:
                    texts.append(chuck)
                    metadatas.append(doc.metadata)
            else:
                texts.append(doc.page_content)
                metadatas.append(doc.metadata)

        if not texts:
            raise HTTPException(
                status_code=500,
                detail="No text content extracted from document"
            )

        # Prepare data for embedding
        embedding_chunk_size = 50
        # Use the provided namespace parameter instead of creating from filename
        print("---------=>", namespace)

        texts = list(texts)
        ids = [str(uuid.uuid4()) for _ in texts]
        metadatas = metadatas or [{} for _ in texts]

        metadatas = [copy.deepcopy(metadata) for metadata in metadatas]
        for metadata, text in zip(metadatas, texts):
            metadata["text"] = text
        for i in range(0, len(texts), embedding_chunk_size):
            chunk_texts = texts[i : i + embedding_chunk_size]
            chunk_ids = ids[i : i + embedding_chunk_size]
            chunk_metadatas = metadatas[i : i + embedding_chunk_size]
            embeddings = embeddings_model.embed_documents(chunk_texts)
            vector_tuples = zip(chunk_ids, embeddings, chunk_metadatas)
        
            # Runs the pinecone upsert asynchronously.
            async_res = [
                index.upsert(
                    vectors=batch_vector_tuples,
                    namespace=namespace,
                    async_req=True,
                )
                for batch_vector_tuples in batch_iterate(32, vector_tuples)
            ]
            [res.get() for res in async_res]

        while True:
            index_stats = index.describe_index_stats()
            if namespace in index_stats["namespaces"]:
                break
            time.sleep(1)

        # Clean up the file
        try:
            os.remove(file_path)
        except Exception as e:
            print(f"Error deleting file {file_path}: {e}")

        print("Training completed successfully")

        return {
            "status": "success",
            "message": "Data training completed successfully",
            "details": {
                "file_name": os.path.basename(file_path),
                "chunks_processed": len(texts),
                "namespace": namespace,
            }
        }
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error during document processing: {str(e)}"
        )


async def process_and_train_batch(file_paths: List[str], case_id: str):
    """
    Process and train multiple documents using Pinecone and OpenAI embeddings.
    This is more efficient than processing files individually as it shares the same
    Pinecone connection and can batch embeddings.
    """
    print(f"Start batch processing and training for {len(file_paths)} files")
    try:
        # Get environment variables
        openai_api_key, pinecone_key, pinecone_index = get_environment_variables()

        # Initialize OpenAI embeddings
        embeddings_model = OpenAIEmbeddings(openai_api_key=openai_api_key)

        # Initialize Pinecone
        pc = Pinecone(api_key=pinecone_key)
        existing_indexes = [index_info["name"]
                            for index_info in pc.list_indexes()]
        print(f"Existing Pinecone indexes: {existing_indexes}")

        index = pc.Index(existing_indexes[0])
        print("index", index)

        # Process all files
        all_texts = []
        all_metadatas = []
        all_namespaces = []

        for file_path in file_paths:
            if not os.path.exists(file_path):
                print(f"File not found: {file_path}, skipping...")
                continue

            print(f"Processing file: {file_path}")

            # Extract file_id from file_path for namespace
            file_id = os.path.splitext(os.path.basename(file_path))[0]
            namespace = f"{case_id}_{file_id}"

            # Load the document
            try:
                if file_path.lower().endswith('.pdf'):
                    loader = PyPDFLoader(file_path=file_path)
                elif file_path.lower().endswith('.docx'):
                    loader = UnstructuredWordDocumentLoader(file_path=file_path)
                elif file_path.lower().endswith('.xlsx'):
                    loader = UnstructuredExcelLoader(file_path=file_path)
                else:
                    print(f"Skipping file {file_path}, not a supported format")
                    continue

                docs = loader.load()

                # Process documents for this file
                file_texts = []
                file_metadatas = []

                # Split documents into appropriate chunks
                for doc in docs:
                    encode_size = string_size_in_kb(doc.page_content)
                    if encode_size > 40:
                        divide_number = int(encode_size / 40) + 1
                        print(f"Splitting large document into {divide_number} chunks")
                        split_encoded_texts = split_text_by_kb(doc.page_content, divide_number)
                        for chunk in split_encoded_texts:
                            file_texts.append(chunk)
                            file_metadatas.append(doc.metadata)
                    else:
                        file_texts.append(doc.page_content)
                        file_metadatas.append(doc.metadata)

                print(f"Processed {len(file_texts)} text chunks from {file_path}")

                # Add to batch collections
                all_texts.extend(file_texts)
                all_metadatas.extend(file_metadatas)
                all_namespaces.extend([namespace] * len(file_texts))

            except Exception as e:
                print(f"Failed to load {file_path}. Error: {e}")
                continue

        if not all_texts:
            raise HTTPException(
                status_code=500,
                detail="No text content extracted from any document"
            )

        print(f"Processing {len(all_texts)} total text chunks from {len(file_paths)} files")

        # Prepare data for embedding
        embedding_chunk_size = 50
        texts = list(all_texts)
        ids = [str(uuid.uuid4()) for _ in texts]
        metadatas = all_metadatas or [{} for _ in texts]

        metadatas = [copy.deepcopy(metadata) for metadata in metadatas]
        for metadata, text in zip(metadatas, texts):
            metadata["text"] = text

        # Process embeddings in batches
        for i in range(0, len(texts), embedding_chunk_size):
            chunk_texts = texts[i : i + embedding_chunk_size]
            chunk_ids = ids[i : i + embedding_chunk_size]
            chunk_metadatas = metadatas[i : i + embedding_chunk_size]
            chunk_namespaces = all_namespaces[i : i + embedding_chunk_size]

            embeddings = embeddings_model.embed_documents(chunk_texts)

            # Group by namespace for efficient upsert
            namespace_groups = {}
            for j, (chunk_id, embedding, metadata, namespace) in enumerate(zip(chunk_ids, embeddings, chunk_metadatas, chunk_namespaces)):
                if namespace not in namespace_groups:
                    namespace_groups[namespace] = []
                namespace_groups[namespace].append((chunk_id, embedding, metadata))

            # Upsert to each namespace
            async_res = []
            for namespace, vectors in namespace_groups.items():
                vector_tuples = vectors
                batch_results = [
                    index.upsert(
                        vectors=batch_vector_tuples,
                        namespace=namespace,
                        async_req=True,
                    )
                    for batch_vector_tuples in batch_iterate(32, vector_tuples)
                ]
                async_res.extend(batch_results)

            # Wait for all upserts to complete
            [res.get() for res in async_res]

        # Wait for all namespaces to be available
        unique_namespaces = list(set(all_namespaces))
        for namespace in unique_namespaces:
            while True:
                index_stats = index.describe_index_stats()
                if namespace in index_stats["namespaces"]:
                    break
                time.sleep(1)

        # Clean up files
        for file_path in file_paths:
            try:
                if os.path.exists(file_path):
                    os.remove(file_path)
            except Exception as e:
                print(f"Error deleting file {file_path}: {e}")

        print("Batch training completed successfully")

        return {
            "status": "success",
            "message": "Batch data training completed successfully",
            "details": {
                "files_processed": len(file_paths),
                "total_chunks_processed": len(texts),
                "namespaces": unique_namespaces,
            }
        }
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error during batch document processing: {str(e)}"
        )
