'use client';

import { useEffect, useState, Suspense } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import Link from 'next/link';
import { Alert } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import {
  CheckCircle,
  AlertCircle,
  Shield,
  ArrowRight,
  RefreshCw,
  Home
} from 'lucide-react';
import { FadeIn, SlideIn, Spinner } from '@/components/ui/loading';

function VerifyContent() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const token = searchParams.get('token');
  const [message, setMessage] = useState('');
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [isVerified, setIsVerified] = useState(false);

  useEffect(() => {
    if (!token) {
      setError('Invalid verification link. Please check your email for the correct link.');
      setIsLoading(false);
      return;
    }

    const verifyEmail = async () => {
      try {
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 2000));

        const res = await fetch(process.env.NEXT_PUBLIC_API_URL + `/auth/verify?token=${token}`);
        const data = await res.json();

        if (!res.ok) throw new Error(data.detail || 'Verification failed');

        setMessage(data.msg || 'Email verified successfully! Your account is now active.');
        setIsVerified(true);

        // Auto-redirect to login after 3 seconds
        setTimeout(() => {
          router.push('/auth/login');
        }, 3000);

      } catch (err: any) {
        setError(err.message || 'Verification failed. The link may be expired or invalid.');
      } finally {
        setIsLoading(false);
      }
    };

    verifyEmail();
  }, [token, router]);

  return (
    <div className="w-full max-w-md mx-auto">
      {/* Header */}
      <FadeIn delay={100}>
        <div className="text-center mb-8">
          <div className="flex justify-center mb-4">
            <div className={`p-4 rounded-full ${
              isLoading ? 'bg-blue-100' :
              isVerified ? 'bg-green-100' : 'bg-red-100'
            }`}>
              {isLoading ? (
                <Spinner size="lg" />
              ) : isVerified ? (
                <CheckCircle className="h-12 w-12 text-green-600" />
              ) : (
                <AlertCircle className="h-12 w-12 text-red-600" />
              )}
            </div>
          </div>

          <h2 className="text-3xl font-bold text-gray-900 mb-2">
            {isLoading ? 'Verifying Email...' :
             isVerified ? 'Email Verified!' : 'Verification Failed'}
          </h2>

          <p className="text-gray-600">
            {isLoading ? 'Please wait while we verify your email address.' :
             isVerified ? 'Your account has been successfully activated.' :
             'There was an issue verifying your email address.'}
          </p>
        </div>
      </FadeIn>

      {/* Loading State */}
      {isLoading && (
        <SlideIn direction="up" delay={200}>
          <div className="bg-blue-50 rounded-lg p-6 mb-6">
            <div className="text-center">
              <div className="flex justify-center mb-4">
                <Spinner size="lg" />
              </div>
              <h3 className="text-lg font-semibold text-blue-900 mb-2">
                Verifying Your Email
              </h3>
              <p className="text-sm text-blue-700">
                This should only take a moment...
              </p>
            </div>
          </div>
        </SlideIn>
      )}

      {/* Success State */}
      {isVerified && !isLoading && (
        <SlideIn direction="up" delay={200}>
          <div className="bg-green-50 rounded-lg p-6 mb-6">
            <div className="text-center">
              <CheckCircle className="h-16 w-16 text-green-600 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-green-900 mb-2">
                Welcome to Insurance Litigation System!
              </h3>
              <p className="text-sm text-green-700 mb-4">
                Your email has been verified and your account is now active.
              </p>
              <div className="text-xs text-green-600">
                Redirecting to login in 3 seconds...
              </div>
            </div>
          </div>
        </SlideIn>
      )}

      {/* Error State */}
      {error && !isLoading && (
        <SlideIn direction="up" delay={200}>
          <div className="bg-red-50 rounded-lg p-6 mb-6">
            <div className="text-center">
              <AlertCircle className="h-16 w-16 text-red-600 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-red-900 mb-2">
                Verification Failed
              </h3>
              <p className="text-sm text-red-700">
                The verification link may be expired or invalid.
              </p>
            </div>
          </div>
        </SlideIn>
      )}

      {/* Messages */}
      {message && (
        <FadeIn delay={0}>
          <Alert className="mb-6 border-green-200 bg-green-50">
            <CheckCircle className="h-4 w-4 text-green-600" />
            <span className="text-green-800">{message}</span>
          </Alert>
        </FadeIn>
      )}

      {error && (
        <FadeIn delay={0}>
          <Alert className="mb-6" variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <span>{error}</span>
          </Alert>
        </FadeIn>
      )}

      {/* Action Buttons */}
      {!isLoading && (
        <SlideIn direction="up" delay={300}>
          <div className="space-y-3">
            {isVerified ? (
              <Button
                onClick={() => router.push('/auth/login')}
                className="w-full hover-lift bg-blue-600 hover:bg-blue-700"
              >
                Continue to Login
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            ) : (
              <>
                <Button
                  onClick={() => router.push('/auth/register')}
                  className="w-full hover-lift bg-blue-600 hover:bg-blue-700"
                >
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Try Again
                </Button>
                <Button
                  onClick={() => router.push('/auth/login')}
                  variant="outline"
                  className="w-full hover-lift"
                >
                  Back to Login
                </Button>
              </>
            )}

            <Button
              onClick={() => router.push('/')}
              variant="outline"
              className="w-full hover-lift"
            >
              <Home className="h-4 w-4 mr-2" />
              Back to Home
            </Button>
          </div>
        </SlideIn>
      )}

      {/* Help Section */}
      <SlideIn direction="up" delay={400}>
        <div className="mt-8 p-4 bg-gray-50 rounded-lg">
          <div className="text-center">
            <p className="text-sm text-gray-600 mb-2">
              Need help with verification?
            </p>
            <Link
              href="/support"
              className="text-sm text-blue-600 hover:text-blue-500 transition-colors"
            >
              Contact Support
            </Link>
          </div>
        </div>
      </SlideIn>

      {/* Security Notice */}
      <SlideIn direction="up" delay={500}>
        <div className="mt-4 p-4 bg-gray-50 rounded-lg">
          <div className="flex items-center text-sm text-gray-600">
            <Shield className="h-4 w-4 mr-2 text-green-600" />
            <span>Email verification helps keep your account secure</span>
          </div>
        </div>
      </SlideIn>
    </div>
  );
}

export default function Verify() {
  return (
    <Suspense fallback={
      <div className="w-full max-w-md mx-auto">
        <FadeIn delay={100}>
          <div className="text-center mb-8">
            <div className="flex justify-center mb-4">
              <div className="p-4 bg-blue-100 rounded-full">
                <Spinner size="lg" />
              </div>
            </div>
            <h2 className="text-3xl font-bold text-gray-900 mb-2">Loading...</h2>
            <p className="text-gray-600">Please wait while we load the verification page.</p>
          </div>
        </FadeIn>
      </div>
    }>
      <VerifyContent />
    </Suspense>
  );
}
