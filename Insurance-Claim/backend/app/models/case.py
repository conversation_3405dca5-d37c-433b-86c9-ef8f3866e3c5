from pydantic import BaseModel
from datetime import datetime, date
from typing import Optional, List
from sqlalchemy import Column, String, Date, DateTime, Text, ForeignKey
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from app.core.database import Base

# SQLAlchemy ORM Model
class Case(Base):
    __tablename__ = "cases"

    id = Column(String, primary_key=True, index=True)
    internal_case_number = Column(String, unique=True, index=True)
    case_name = Column(String, nullable=False)
    date_of_loss = Column(Date, nullable=True)
    date_of_lawsuit = Column(Date, nullable=True)
    date_of_noc = Column(Date, nullable=True)
    date_received_from_carrier = Column(Date, nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Foreign key to user who created the case
    user_id = Column(String, ForeignKey("users.id"), nullable=False)
    
    # Foreign key to collection (optional)
    collection_id = Column(String, ForeignKey("collections.id"), nullable=True)
    
    # Relationships
    user = relationship("User", back_populates="cases")
    collection = relationship("Collection", back_populates="cases")
    files = relationship("CaseFile", back_populates="case", cascade="all, delete-orphan")

# Pydantic Models for API
class CaseBase(BaseModel):
    internal_case_number: Optional[str] = None
    case_name: str
    date_of_loss: Optional[date] = None
    date_of_lawsuit: Optional[date] = None
    date_of_noc: Optional[date] = None
    date_received_from_carrier: date
    collection_id: Optional[str] = None

class CaseCreate(CaseBase):
    pass

class CaseUpdate(BaseModel):
    internal_case_number: Optional[str] = None
    case_name: Optional[str] = None
    date_of_loss: Optional[date] = None
    date_of_lawsuit: Optional[date] = None
    date_of_noc: Optional[date] = None
    date_received_from_carrier: Optional[date] = None
    collection_id: Optional[str] = None

class CaseResponse(CaseBase):
    id: str
    user_id: str
    created_at: datetime
    updated_at: Optional[datetime] = None
    files_count: int = 0

    class Config:
        from_attributes = True

class CaseWithProcessingStatus(CaseResponse):
    """Extended case response with document processing status"""
    processing_status: str  # "not_started", "in_progress", "completed", "error"
    processing_progress: int  # 0-100
    total_files: int
    processed_files: int
    pending_files: int
    error_files: int
    collection_name: Optional[str] = None

    class Config:
        from_attributes = True