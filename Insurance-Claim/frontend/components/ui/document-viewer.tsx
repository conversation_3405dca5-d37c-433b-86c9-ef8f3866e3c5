'use client';

import { useState } from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import {
  Download,
  Share,
  Bookmark,
  Highlighter,
  ChevronDown,
  FileText
} from 'lucide-react';

interface Document {
  id: string;
  family: string;
  title: string;
  mark: string;
  issues: string;
  reviewer: string;
  size: string;
  type: 'log' | 'doc' | 'email' | 'policy' | 'medical' | 'claims' | 'bills' | 'lawsuit' | 'pdf' | 'image';
  status: 'reviewed' | 'pending' | 'flagged';
  uploadDate: string;
  pageCount?: number;
  reports?: string[];
}

interface ReportTemplate {
  id: string;
  name: string;
  description: string;
  content: {
    header: {
      title: string;
      subtitle: string;
      organization: string;
    };
    body: {
      title: string;
      sections: Array<{
        title: string;
        content: string;
        highlighted?: boolean;
      }>;
    };
  };
}

interface DocumentViewerProps {
  document: Document;
  currentPage: number;
  zoomLevel: number;
  selectedTool: 'select' | 'highlight' | 'redact';
  selectedColor: string;
  onPageChange?: (page: number) => void;
  onZoomChange?: (zoom: number) => void;
  onReportChange?: (reportId: string) => void;
  className?: string;
}

// Report templates based on document type
const getReportTemplates = (documentType: string): ReportTemplate[] => {
  switch (documentType) {
    case 'medical':
      return [
        {
          id: 'medical-summary',
          name: 'Medical Summary Report',
          description: 'Comprehensive medical assessment and treatment summary',
          content: {
            header: {
              title: 'MEDICAL REPORT #2024-001',
              subtitle: 'Patient Assessment and Treatment Summary',
              organization: 'Regional Medical Center - Department of Orthopedics'
            },
            body: {
              title: 'Patient Medical Assessment',
              sections: [
                {
                  title: 'Patient Information',
                  content: 'Patient: John Smith, DOB: 03/15/1985, Case #: MED-2024-001. Initial presentation on January 15, 2024, following motor vehicle accident with complaints of lower back pain and limited mobility.',
                  highlighted: true
                },
                {
                  title: 'Clinical Findings',
                  content: 'Physical examination revealed tenderness in the L4-L5 region with restricted range of motion. MRI imaging shows mild disc herniation at L4-L5 level with no significant nerve compression. Patient reports pain level 6/10 on visual analog scale.'
                },
                {
                  title: 'Treatment Plan',
                  content: 'Recommended conservative treatment including physical therapy, anti-inflammatory medication, and activity modification. Follow-up appointment scheduled in 4 weeks to assess progress and determine need for additional interventions.'
                }
              ]
            }
          }
        },
        {
          id: 'medical-diagnostic',
          name: 'Diagnostic Report',
          description: 'Detailed diagnostic findings and test results',
          content: {
            header: {
              title: 'DIAGNOSTIC REPORT #2024-001',
              subtitle: 'Imaging and Laboratory Results',
              organization: 'Regional Medical Center - Diagnostic Imaging Department'
            },
            body: {
              title: 'Diagnostic Assessment Results',
              sections: [
                {
                  title: 'Imaging Results',
                  content: 'MRI Lumbar Spine: Mild disc herniation at L4-L5 level. No significant spinal stenosis or nerve root compression identified. Vertebral alignment appears normal.',
                  highlighted: true
                },
                {
                  title: 'Laboratory Findings',
                  content: 'Complete blood count within normal limits. Inflammatory markers (ESR, CRP) slightly elevated, consistent with acute injury response. No signs of infection or systemic disease.'
                }
              ]
            }
          }
        }
      ];
    case 'claims':
      return [
        {
          id: 'claims-summary',
          name: 'Insurance Claim Summary',
          description: 'Overview of claim details and coverage assessment',
          content: {
            header: {
              title: 'INSURANCE CLAIM #IC-2024-5678',
              subtitle: 'Motor Vehicle Accident Claim Assessment',
              organization: 'Premier Insurance Company - Claims Investigation Division'
            },
            body: {
              title: 'Claim Assessment Report',
              sections: [
                {
                  title: 'Incident Details',
                  content: 'Date of Loss: January 15, 2024. Location: Interstate 95, Mile Marker 42. Claimant vehicle rear-ended by third party. Police report #PR-2024-0115 filed. No fatalities reported.',
                  highlighted: true
                },
                {
                  title: 'Coverage Analysis',
                  content: 'Policy #POL-2024-001 active at time of loss. Coverage includes collision ($500 deductible), comprehensive, and medical payments ($5,000 limit). Liability coverage: $100,000/$300,000.'
                },
                {
                  title: 'Damages Assessment',
                  content: 'Vehicle damage estimate: $8,450. Medical expenses to date: $3,200. Lost wages claimed: $1,800. Total claim amount under review: $13,450.'
                }
              ]
            }
          }
        }
      ];
    case 'bills':
      return [
        {
          id: 'billing-summary',
          name: 'Medical Billing Summary',
          description: 'Detailed breakdown of medical expenses and charges',
          content: {
            header: {
              title: 'MEDICAL BILLING STATEMENT',
              subtitle: 'Account Summary and Charges',
              organization: 'Regional Medical Center - Patient Financial Services'
            },
            body: {
              title: 'Billing Summary Report',
              sections: [
                {
                  title: 'Service Summary',
                  content: 'Emergency Department Visit: $850. MRI Lumbar Spine: $1,200. Physical Therapy (6 sessions): $720. Physician Consultations: $430. Total Charges: $3,200.',
                  highlighted: true
                },
                {
                  title: 'Insurance Processing',
                  content: 'Primary Insurance: Premier Insurance Company. Claim submitted: January 20, 2024. Insurance payment: $2,560. Patient responsibility: $640 (deductible and co-pay).'
                }
              ]
            }
          }
        }
      ];
    case 'lawsuit':
      return [
        {
          id: 'lawsuit-summary',
          name: 'Legal Case Summary',
          description: 'Overview of lawsuit proceedings and key details',
          content: {
            header: {
              title: 'LEGAL CASE FILING #2024-CV-001',
              subtitle: 'Personal Injury Lawsuit Documentation',
              organization: 'Superior Court of Justice - Civil Division'
            },
            body: {
              title: 'Lawsuit Summary Report',
              sections: [
                {
                  title: 'Case Information',
                  content: 'Plaintiff: John Smith vs. Defendant: ABC Transportation Inc. Case filed: January 19, 2024. Cause of action: Motor vehicle negligence resulting in personal injury. Damages sought: $150,000.',
                  highlighted: true
                },
                {
                  title: 'Legal Claims',
                  content: 'Primary claims include negligent operation of motor vehicle, failure to maintain safe following distance, and resulting personal injuries including back trauma and lost wages.'
                },
                {
                  title: 'Discovery Status',
                  content: 'Initial discovery requests served. Medical records subpoenaed. Expert witness depositions scheduled for March 2024. Trial date set for September 2024.'
                }
              ]
            }
          }
        }
      ];
    case 'log':
      return [
        {
          id: 'log-summary',
          name: 'Claim File Activity Log',
          description: 'Chronological record of claim file activities and updates',
          content: {
            header: {
              title: 'CLAIM FILE ACTIVITY LOG #IC-2024-5678',
              subtitle: 'Chronological Activity Record',
              organization: 'Premier Insurance Company - Claims Management System'
            },
            body: {
              title: 'Activity Log Summary',
              sections: [
                {
                  title: 'Recent Activities',
                  content: 'Jan 15, 2024: Claim opened by John Smith. Jan 16, 2024: Initial investigation assigned to Mike Johnson. Jan 17, 2024: Medical records requested. Jan 18, 2024: Vehicle inspection scheduled.',
                  highlighted: true
                },
                {
                  title: 'Key Milestones',
                  content: 'Claim intake completed, investigation initiated, medical documentation in progress, liability assessment pending.'
                },
                {
                  title: 'Next Actions',
                  content: 'Complete vehicle damage assessment, obtain final medical reports, schedule independent medical examination, prepare settlement evaluation.'
                }
              ]
            }
          }
        }
      ];
    case 'email':
      return [
        {
          id: 'email-summary',
          name: 'Email Communication Summary',
          description: 'Summary of email correspondence and key communications',
          content: {
            header: {
              title: 'EMAIL COMMUNICATION THREAD',
              subtitle: 'Adjuster and Claimant Correspondence',
              organization: 'Premier Insurance Company - Claims Communication'
            },
            body: {
              title: 'Email Thread Analysis',
              sections: [
                {
                  title: 'Communication Summary',
                  content: 'Email thread between Claims Adjuster Mike Johnson and Claimant John Smith. Period: Jan 15-20, 2024. Total messages: 8. Key topics: Claim status, documentation requests, settlement discussions.',
                  highlighted: true
                },
                {
                  title: 'Key Discussions',
                  content: 'Initial claim notification, request for additional documentation, clarification of policy coverage, discussion of repair estimates and medical expenses.'
                },
                {
                  title: 'Action Items',
                  content: 'Claimant to provide additional medical records, adjuster to review repair estimates, schedule follow-up call for settlement discussion.'
                }
              ]
            }
          }
        }
      ];
    case 'policy':
      return [
        {
          id: 'policy-summary',
          name: 'Insurance Policy Summary',
          description: 'Overview of policy coverage and relevant terms',
          content: {
            header: {
              title: 'AUTO INSURANCE POLICY #POL-2024-001',
              subtitle: 'Coverage Summary and Terms',
              organization: 'Premier Insurance Company - Policy Administration'
            },
            body: {
              title: 'Policy Coverage Analysis',
              sections: [
                {
                  title: 'Coverage Details',
                  content: 'Liability: $100,000/$300,000. Collision: $500 deductible. Comprehensive: $250 deductible. Medical Payments: $5,000. Uninsured Motorist: $100,000/$300,000. Policy effective: Jan 1, 2024 - Jan 1, 2025.',
                  highlighted: true
                },
                {
                  title: 'Relevant Exclusions',
                  content: 'Standard exclusions apply including intentional acts, racing, commercial use, and driving under the influence. No specific exclusions applicable to current claim.'
                },
                {
                  title: 'Claims History',
                  content: 'No prior claims in current policy period. Clean driving record. Policy in good standing with all premiums current.'
                }
              ]
            }
          }
        }
      ];
    case 'doc':
      return [
        {
          id: 'memo-summary',
          name: 'Internal Document Summary',
          description: 'Summary of internal company documentation',
          content: {
            header: {
              title: 'INTERNAL COMPANY MEMORANDUM',
              subtitle: 'Claims Department Communication',
              organization: 'Premier Insurance Company - Internal Communications'
            },
            body: {
              title: 'Internal Memo Analysis',
              sections: [
                {
                  title: 'Memo Summary',
                  content: 'Internal memo from Claims Manager to Adjusters regarding new claim handling procedures. Date: January 22, 2024. Subject: Enhanced documentation requirements for motor vehicle claims.',
                  highlighted: true
                },
                {
                  title: 'Key Directives',
                  content: 'All motor vehicle claims require enhanced photo documentation, independent damage assessments for claims over $5,000, and mandatory medical record review for injury claims.'
                },
                {
                  title: 'Implementation',
                  content: 'New procedures effective immediately. Training sessions scheduled for all adjusters. Updated claim handling guidelines distributed to all staff.'
                }
              ]
            }
          }
        }
      ];
    default:
      return [
        {
          id: 'structural-report',
          name: 'Structural Analysis Report',
          description: 'Technical assessment and findings',
          content: {
            header: {
              title: 'NUMBER NO. 2006',
              subtitle: 'New York\'s Food and Life Sciences Bulletin',
              organization: 'New York State Agricultural Experiment Station, Geneva, a Division of the New York State College of Agriculture and Life Sciences, a Statutory College of the State University, at Cornell University'
            },
            body: {
              title: '\'Noiret\'™ Grape',
              sections: [
                {
                  title: 'Research Team',
                  content: 'B.I. Reisch, R.S. Luce, Bruce Bordelon, and T. Henick-Kling',
                  highlighted: true
                },
                {
                  title: 'Variety Description',
                  content: '\'Noiret\' (pronounced "nwah-RAY") is a mid-season red wine grape suitable for the production of varietal wines. The distinctive characteristic of this selection is the absence of hybrid aromas and notes of green and black pepper along with raspberry, blackberry, and some mint aromas.'
                },
                {
                  title: 'Characteristics',
                  content: 'A major distinguishing characteristic of this selection is that the tannin structure and the absence of any hybrid aromas strongly distinguish this selection from other red hybrid grapes. The vine is moderately winter hardy and moderately resistant to powdery mildew and botrytis.'
                }
              ]
            }
          }
        }
      ];
  }
};

export function DocumentViewer({
  document,
  currentPage,
  zoomLevel,
  selectedTool,
  selectedColor,
  onReportChange,
  className
}: DocumentViewerProps) {
  const [selectedReportId, setSelectedReportId] = useState<string>('');
  const [showReportDropdown, setShowReportDropdown] = useState(false);

  const availableReports = getReportTemplates(document.type);
  const selectedReport = availableReports.find(r => r.id === selectedReportId) || availableReports[0];

  // Initialize with first report if none selected
  if (!selectedReportId && availableReports.length > 0) {
    setSelectedReportId(availableReports[0].id);
  }

  // Fallback if no reports are available
  if (!selectedReport) {
    return (
      <div className={cn("bg-white shadow-lg rounded-lg overflow-hidden", className)}>
        <div className="p-8 text-center">
          <FileText className="h-16 w-16 mx-auto mb-4 text-gray-400" />
          <h3 className="text-lg font-semibold text-gray-900 mb-2">No Report Available</h3>
          <p className="text-gray-600">No report templates are configured for this document type.</p>
        </div>
      </div>
    );
  }

  const handleReportChange = (reportId: string) => {
    setSelectedReportId(reportId);
    setShowReportDropdown(false);
    onReportChange?.(reportId);
  };
  return (
    <div className={cn("bg-white shadow-lg rounded-lg overflow-hidden", className)}>
      {/* Document Header */}
      <div className="bg-gray-50 border-b border-gray-200 p-4">
        <div className="flex items-center justify-between">
          <div className="flex-1">
            <h3 className="text-lg font-semibold text-gray-900">{document.title}</h3>
            <p className="text-sm text-gray-600">
              Page {currentPage} of {document.pageCount || 1} • {document.size}
            </p>
          </div>

          {/* Report Selector */}
          {availableReports.length > 1 && (
            <div className="mx-4">
              <div className="relative">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowReportDropdown(!showReportDropdown)}
                  className="min-w-[200px] justify-between"
                >
                  <span className="flex items-center">
                    <FileText className="h-4 w-4 mr-2" />
                    {selectedReport?.name || 'Select Report'}
                  </span>
                  <ChevronDown className="h-4 w-4" />
                </Button>

                {showReportDropdown && (
                  <div className="absolute top-full left-0 mt-1 w-full bg-white border border-gray-200 rounded-md shadow-lg z-10">
                    {availableReports.map((report) => (
                      <button
                        key={report.id}
                        onClick={() => handleReportChange(report.id)}
                        className={cn(
                          "w-full text-left px-3 py-2 text-sm hover:bg-gray-50 first:rounded-t-md last:rounded-b-md",
                          selectedReportId === report.id && "bg-blue-50 text-blue-700"
                        )}
                      >
                        <div className="font-medium">{report.name}</div>
                        <div className="text-xs text-gray-500">{report.description}</div>
                      </button>
                    ))}
                  </div>
                )}
              </div>
            </div>
          )}

          <div className="flex items-center space-x-2">
            <Button variant="outline" size="sm">
              <Download className="h-4 w-4 mr-1" />
              Download
            </Button>
            <Button variant="outline" size="sm">
              <Share className="h-4 w-4 mr-1" />
              Share
            </Button>
            <Button variant="outline" size="sm">
              <Bookmark className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Document Content */}
      <div
        className="relative bg-gray-100 p-8 min-h-[600px] overflow-auto"
        style={{ transform: `scale(${zoomLevel / 100})`, transformOrigin: 'top center' }}
      >
        <div className="max-w-4xl mx-auto bg-white shadow-lg rounded-lg overflow-hidden">
          {/* Dynamic Report Header */}
          <div className={cn(
            "text-white p-8 text-center",
            document.type === 'medical' ? 'bg-red-600' :
            document.type === 'claims' ? 'bg-blue-600' :
            document.type === 'bills' ? 'bg-green-600' :
            'bg-red-600'
          )}>
            <h2 className="text-3xl font-bold mb-3">{selectedReport?.content.header.title}</h2>
            <h3 className="text-xl mb-4">{selectedReport?.content.header.subtitle}</h3>
            <p className="text-sm leading-relaxed">
              {selectedReport?.content.header.organization}
            </p>
          </div>

          {/* Dynamic Report Body */}
          <div className="p-8 bg-white">
            <h1 className="text-4xl font-bold text-gray-900 mb-8">{selectedReport?.content.body.title}</h1>

            <div className="space-y-6 text-gray-700 leading-relaxed">
              {selectedReport?.content.body.sections.map((section, index) => (
                <div key={index}>
                  {section.highlighted ? (
                    <p className="font-semibold text-lg">
                      <span className="font-medium text-gray-900 mr-2">{section.title}:</span>
                      <span
                        className="bg-yellow-200 px-1 rounded"
                        style={{ backgroundColor: selectedColor }}
                      >
                        {section.content}
                      </span>
                    </p>
                  ) : (
                    <div>
                      <h3 className="text-xl font-semibold text-gray-900 mb-3">{section.title}</h3>
                      <p className="text-justify">{section.content}</p>
                    </div>
                  )}
                </div>
              ))}

              {/* Highlighted text example */}
              <div className="bg-yellow-100 border-l-4 border-yellow-500 p-4 my-6">
                <p className="text-sm text-gray-600 mb-2 font-semibold">
                  <Highlighter className="h-4 w-4 inline mr-1" />
                  Highlighted Text:
                </p>
                <p className="text-gray-800 italic">
                  "{selectedReport?.content.body.sections.find(s => s.highlighted)?.content.substring(0, 100)}..."
                </p>
                <p className="text-xs text-gray-500 mt-2">
                  Highlighted by {document.reviewer} on {document.uploadDate}
                </p>
              </div>

              {/* Additional Information section */}
              <div className="mt-8">
                <div className="flex justify-between items-center mb-4 pb-2 border-b border-gray-200">
                  <h3 className="text-xl font-semibold text-gray-900">Additional Information</h3>
                  <div className="flex space-x-2">
                    <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">
                      Highlighted
                    </Badge>
                    <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                      {document.type.charAt(0).toUpperCase() + document.type.slice(1)}
                    </Badge>
                  </div>
                </div>

                <p className="text-gray-700">
                  This {selectedReport?.name.toLowerCase()} contains detailed information relevant to the insurance claim review process.
                  All highlighted sections require special attention during the review workflow.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Document Footer */}
      <div className="bg-gray-50 border-t border-gray-200 p-4">
        <div className="flex items-center justify-between text-sm text-gray-600">
          <div className="flex items-center space-x-4">
            <span>Tool: {selectedTool}</span>
            {selectedTool !== 'select' && (
              <span className="flex items-center">
                Color:
                <div
                  className="w-4 h-4 rounded border ml-1"
                  style={{ backgroundColor: selectedColor }}
                />
              </span>
            )}
            <span>Report: {selectedReport?.name}</span>
          </div>
          <div className="flex items-center space-x-2">
            <span>Document viewer active</span>
            <span>•</span>
            <span>{document.type.charAt(0).toUpperCase() + document.type.slice(1)} Document</span>
          </div>
        </div>
      </div>
    </div>
  );
}

export default DocumentViewer;
