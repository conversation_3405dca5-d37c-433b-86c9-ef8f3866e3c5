'use client';

import React, { useState, useEffect } from 'react';
import { apiClient, CaseWithProcessingStatus, CaseFileResponse } from '@/lib/api';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import {
  Search,
  Filter,
  FileText,
  Calendar,
  FolderOpen,
  AlertCircle,
  CheckCircle,
  Clock,
  XCircle,
  Eye,
  Edit,
  MoreHorizontal,
  Plus,
  List,
  Grid3X3,
  Download,
  X,
  File,
  BookOpen,

  Hash
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface Notification {
  id: string;
  type: 'success' | 'error' | 'info' | 'warning';
  title: string;
  message: string;
  duration?: number;
}

const AllCasesPage = () => {
  // Check if coming from upload immediately
  const isFromUpload = typeof window !== 'undefined' &&
    (new URLSearchParams(window.location.search).get('from') === 'upload' ||
     sessionStorage.getItem('justUploaded') === 'true');

  const [cases, setCases] = useState<CaseWithProcessingStatus[]>([]);
  const [loading, setLoading] = useState(!isFromUpload); // Don't start with loading if from upload
  const [showFullScreenLoading, setShowFullScreenLoading] = useState(!isFromUpload);
  const [showHeaderLoading, setShowHeaderLoading] = useState(!isFromUpload);
  const [showCasesImmediately, setShowCasesImmediately] = useState(isFromUpload);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [viewMode, setViewMode] = useState<'list' | 'grid'>('list');
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [isPolling, setIsPolling] = useState(false);

  // Document viewer state
  const [showDocumentModal, setShowDocumentModal] = useState(false);
  const [selectedCase, setSelectedCase] = useState<CaseWithProcessingStatus | null>(null);
  const [caseFiles, setCaseFiles] = useState<CaseFileResponse[]>([]);
  const [loadingFiles, setLoadingFiles] = useState(false);

  // Case details modal state
  const [showCaseDetailsModal, setShowCaseDetailsModal] = useState(false);
  const [selectedCaseForDetails, setSelectedCaseForDetails] = useState<CaseWithProcessingStatus | null>(null);

  // Load cases on component mount
  useEffect(() => {
    // Clear the upload flag if it exists
    if (isFromUpload) {
      sessionStorage.removeItem('justUploaded');
    }

    // Load cases with appropriate loading behavior
    if (isFromUpload) {
      // Load cases immediately to show embedding progress - force no loading states
      loadCases(false, true, true); // forceNoLoading = true

      // Show notification about embedding progress
      setTimeout(() => {
        showNotification({
          type: 'info',
          title: 'Embedding in Progress',
          message: 'Your files are being processed for search and analysis. Progress is shown below.',
          duration: 4000
        });
      }, 500);
    } else {
      // For normal visits, load cases with normal loading indicators
      loadCases(true, true);
    }

    // Also set a timeout to hide full screen loading after 2 seconds regardless
    // This ensures the page becomes usable even if there are any issues
    const timeoutId = setTimeout(() => {
      setShowFullScreenLoading(false);
    }, 2000);

    return () => clearTimeout(timeoutId);
  }, [isFromUpload]); // Add isFromUpload as dependency

  // Polling effect for in-progress cases
  useEffect(() => {
    let intervalId: NodeJS.Timeout;

    if (isPolling) {
      intervalId = setInterval(pollCasesStatus, 3000); // Poll every 3 seconds
    }

    return () => {
      if (intervalId) {
        clearInterval(intervalId);
      }
    };
  }, [isPolling, cases]);

  const loadCases = async (showHeaderLoadingIndicator = true, isInitialLoad = false, forceNoLoading = false) => {
    try {
      // If forceNoLoading is true (coming from upload), never set loading states
      if (forceNoLoading) {
        setLoading(false);
        setShowFullScreenLoading(false);
        setShowHeaderLoading(false);
      } else if (!showCasesImmediately) {
        setLoading(true);
        if (showHeaderLoadingIndicator) {
          setShowHeaderLoading(true);
        }
      } else {
        // When coming from upload, don't set loading to true
        setLoading(false);
      }

      const casesData = await apiClient.getCasesWithStatus();

      // Deduplicate cases by ID to prevent React key conflicts
      const uniqueCases = casesData.filter((case_, index, self) =>
        index === self.findIndex(c => c.id === case_.id)
      );

      // Log warning if duplicates were found
      if (casesData.length !== uniqueCases.length) {
        console.warn(`Found ${casesData.length - uniqueCases.length} duplicate cases. Deduplicating...`);
      }

      setCases(uniqueCases);

      // Check if any cases are in progress to determine if we should poll
      const hasInProgressCases = casesData.some(case_ => case_.processing_status === 'in_progress');
      setIsPolling(hasInProgressCases);

      // If this is initial load and we're coming from upload, start polling immediately
      if (isInitialLoad && showCasesImmediately && hasInProgressCases) {
        setIsPolling(true);
      }

    } catch (error) {
      console.error('Error loading cases:', error);
      showNotification({
        type: 'error',
        title: 'Error',
        message: 'Failed to load cases. Please try again.'
      });
    } finally {
      // Only update loading states if not coming from upload and not forcing no loading
      if (!showCasesImmediately && !forceNoLoading) {
        setLoading(false);
        setShowFullScreenLoading(false);
        setShowHeaderLoading(false);
      }
    }
  };

  const pollCasesStatus = async () => {
    try {
      const casesData = await apiClient.getCasesWithStatus();

      // Check if any cases are still in progress
      const hasInProgressCases = casesData.some(case_ => case_.processing_status === 'in_progress');
      setIsPolling(hasInProgressCases);

      // Show notification when a case completes processing
      const previousCases = cases;
      casesData.forEach(currentCase => {
        const previousCase = previousCases.find(c => c.id === currentCase.id);
        if (previousCase &&
            previousCase.processing_status === 'in_progress' &&
            currentCase.processing_status === 'completed') {
          showNotification({
            type: 'success',
            title: 'Processing Complete',
            message: `Case "${currentCase.case_name}" has finished processing all documents.`
          });
        }
      });

      // Deduplicate cases by ID to prevent React key conflicts
      const uniqueCases = casesData.filter((case_, index, self) =>
        index === self.findIndex(c => c.id === case_.id)
      );

      // Log warning if duplicates were found
      if (casesData.length !== uniqueCases.length) {
        console.warn(`Polling: Found ${casesData.length - uniqueCases.length} duplicate cases. Deduplicating...`);
      }

      setCases(uniqueCases);

    } catch (error) {
      console.error('Failed to poll cases status:', error);
    }
  };

  const showNotification = (notification: Omit<Notification, 'id'>) => {
    const id = Date.now().toString();
    const newNotification = { ...notification, id };
    setNotifications(prev => [...prev, newNotification]);

    // Auto-remove notification after duration
    setTimeout(() => {
      setNotifications(prev => prev.filter(n => n.id !== id));
    }, notification.duration || 5000);
  };

  const handleViewDocuments = async (case_: CaseWithProcessingStatus) => {
    try {
      setSelectedCase(case_);
      setLoadingFiles(true);
      setShowDocumentModal(true);

      const files = await apiClient.getCaseFiles(case_.id);
      setCaseFiles(files);
    } catch (error) {
      console.error('Error loading case files:', error);
      showNotification({
        type: 'error',
        title: 'Error',
        message: 'Failed to load case documents. Please try again.'
      });
    } finally {
      setLoadingFiles(false);
    }
  };

  const handleCloseDocumentModal = () => {
    setShowDocumentModal(false);
    setSelectedCase(null);
    setCaseFiles([]);
  };

  const handleDocumentReview = () => {
    // Navigate to document review page without specific case context
    // Clear any previous case selection
    sessionStorage.removeItem('selectedCaseForReview');

    // Navigate to document review page
    window.location.href = '/documents_review';
  };

  const handleViewCaseDetails = (case_: CaseWithProcessingStatus) => {
    setSelectedCaseForDetails(case_);
    setShowCaseDetailsModal(true);
  };

  const handleCloseCaseDetailsModal = () => {
    setShowCaseDetailsModal(false);
    setSelectedCaseForDetails(null);
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getFileIcon = (fileType: string) => {
    if (fileType.includes('pdf')) return <File className="h-4 w-4 text-red-500" />;
    if (fileType.includes('image')) return <File className="h-4 w-4 text-blue-500" />;
    return <FileText className="h-4 w-4 text-gray-500" />;
  };

  // Filter cases based on search term and status
  const filteredCases = cases.filter(case_ => {
    const matchesSearch = case_.case_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         case_.internal_case_number?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         case_.collection_name?.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = statusFilter === 'all' || case_.processing_status === statusFilter;
    
    return matchesSearch && matchesStatus;
  });

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'in_progress':
        return <Clock className="h-4 w-4 text-blue-600" />;
      case 'error':
        return <XCircle className="h-4 w-4 text-red-600" />;
      case 'not_started':
        return <AlertCircle className="h-4 w-4 text-gray-400" />;
      default:
        return <AlertCircle className="h-4 w-4 text-gray-400" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const variants = {
      completed: 'bg-green-100 text-green-800 border-green-200',
      in_progress: 'bg-blue-100 text-blue-800 border-blue-200',
      error: 'bg-red-100 text-red-800 border-red-200',
      not_started: 'bg-gray-100 text-gray-800 border-gray-200'
    };

    const labels = {
      completed: 'Completed',
      in_progress: 'In Progress',
      error: 'Error',
      not_started: 'Not Started'
    };

    return (
      <Badge className={cn('border', variants[status as keyof typeof variants])}>
        {getStatusIcon(status)}
        <span className="ml-1">{labels[status as keyof typeof labels]}</span>
      </Badge>
    );
  };



  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };





  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
      {/* Header */}
      <div className="bg-white/80 backdrop-blur-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-6 py-6">
          <div className="flex items-center justify-between">
            <div>
              <div className="flex items-center space-x-3">
                <h1 className="text-3xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent">
                  All Cases
                </h1>
                {/* {loading && showHeaderLoading && (
                  <div className="flex items-center space-x-2">
                    <div className="w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
                    <span className="text-sm text-blue-600 font-medium">Updating...</span>
                  </div>
                )} */}
              </div>
              <p className="text-sm text-gray-500 mt-1">Manage and monitor all your cases and their processing status</p>
            </div>
            <div className="flex items-center space-x-4">
              <Button
                onClick={() => window.location.href = '/create-case'}
                variant="default"
                className="bg-blue-600 hover:bg-blue-700 text-white"
              >
                <Plus className="h-4 w-4 mr-2" />
                Create Case
              </Button>
              <Button
                onClick={handleDocumentReview}
                variant="outline"
                className="border-green-200 text-green-700 hover:bg-green-50"
              >
                <BookOpen className="h-4 w-4 mr-2" />
                Document Review
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-6 py-8">
        {/* Summary Statistics */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
          <div className="bg-white/70 backdrop-blur-sm rounded-lg border border-gray-200 p-4">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <FileText className="h-8 w-8 text-blue-600" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-500">Total Cases</p>
                <p className="text-2xl font-semibold text-gray-900">{cases.length}</p>
              </div>
            </div>
          </div>

          <div className="bg-white/70 backdrop-blur-sm rounded-lg border border-gray-200 p-4">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <CheckCircle className="h-8 w-8 text-green-600" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-500">Completed</p>
                <p className="text-2xl font-semibold text-gray-900">
                  {cases.filter(c => c.processing_status === 'completed').length}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white/70 backdrop-blur-sm rounded-lg border border-gray-200 p-4">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Clock className="h-8 w-8 text-blue-600" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-500">In Progress</p>
                <p className="text-2xl font-semibold text-gray-900">
                  {cases.filter(c => c.processing_status === 'in_progress').length}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white/70 backdrop-blur-sm rounded-lg border border-gray-200 p-4">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <XCircle className="h-8 w-8 text-red-600" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-500">With Errors</p>
                <p className="text-2xl font-semibold text-gray-900">
                  {cases.filter(c => c.processing_status === 'error').length}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Filters */}
        <div className="mb-6 flex flex-col sm:flex-row gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search cases by name, number, or collection..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 bg-white/70 backdrop-blur-sm border-gray-200"
            />
          </div>
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <Filter className="h-4 w-4 text-gray-500" />
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="px-3 py-2 border border-gray-200 rounded-md bg-white/70 backdrop-blur-sm text-sm"
              >
                <option value="all">All Status</option>
                <option value="not_started">Not Started</option>
                <option value="in_progress">In Progress</option>
                <option value="completed">Completed</option>
                <option value="error">Error</option>
              </select>
            </div>

            {/* View Toggle */}
            <div className="flex items-center border border-gray-200 rounded-md bg-white/70 backdrop-blur-sm">
              <Button
                variant={viewMode === 'list' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setViewMode('list')}
                className="rounded-r-none border-r border-gray-200"
              >
                <List className="h-4 w-4" />
              </Button>
              <Button
                variant={viewMode === 'grid' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setViewMode('grid')}
                className="rounded-l-none"
              >
                <Grid3X3 className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>

        {/* Cases Display */}
        {false ? ( // Never show skeleton loading - always show cases list
          <div className="space-y-4">
            <div className="text-center py-4">
              <div className="flex items-center justify-center space-x-2 mb-4">
                <div className="w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
                <span className="text-blue-600 font-medium">Loading cases...</span>
              </div>
            </div>
            {/* Skeleton Cards */}
            {[1, 2, 3].map((i) => (
              <div key={i} className="bg-white/80 backdrop-blur-sm rounded-xl shadow-sm border border-gray-100 p-6">
                <div className="animate-pulse">
                  <div className="flex items-center justify-between mb-4">
                    <div className="h-6 bg-gray-200 rounded w-1/3"></div>
                    <div className="h-5 bg-gray-200 rounded w-20"></div>
                  </div>
                  <div className="space-y-3">
                    <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <div className="h-3 bg-gray-200 rounded w-24"></div>
                        <div className="h-3 bg-gray-200 rounded w-12"></div>
                      </div>
                      <div className="h-2 bg-gray-200 rounded w-full"></div>
                      <div className="h-3 bg-gray-200 rounded w-32"></div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : filteredCases.length === 0 ? (
          <div className="text-center py-12">
            <FileText className="h-16 w-16 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No cases found</h3>
            <p className="text-gray-500">
              {searchTerm || statusFilter !== 'all'
                ? 'Try adjusting your search or filter criteria.'
                : 'Create your first case to get started.'}
            </p>
          </div>
        ) : viewMode === 'list' ? (
          // List View
          <div className="bg-white/70 backdrop-blur-sm rounded-lg border border-gray-200 overflow-hidden">
            {/* Table Header - Hidden on mobile */}
            <div className="hidden lg:block bg-gray-50/80 px-6 py-4 border-b border-gray-200">
              <div className="grid grid-cols-12 gap-4 text-sm font-medium text-gray-700">
                <div className="col-span-3">Case Information</div>
                <div className="col-span-2">Collection</div>
                <div className="col-span-2">Status</div>
                <div className="col-span-3">Processing Progress</div>
                <div className="col-span-1">Files</div>
                <div className="col-span-1">Actions</div>
              </div>
            </div>

            {/* Table Body */}
            <div className="divide-y divide-gray-200">
              {filteredCases.map((case_, index) => (
                <div key={`${case_.id}-${index}`} className="px-4 lg:px-6 py-4 hover:bg-gray-50/50 transition-colors duration-150">
                  {/* Desktop Layout */}
                  <div className="hidden lg:grid lg:grid-cols-12 lg:gap-4 lg:items-center">
                    {/* Case Information */}
                    <div className="col-span-3">
                      <div className="flex flex-col">
                        <h3 className="font-semibold text-gray-900 truncate" title={case_.case_name}>
                          {case_.case_name}
                        </h3>
                        <p className="text-sm text-gray-500 mt-1">
                          {case_.internal_case_number || 'No case number'}
                        </p>
                        <p className="text-xs text-gray-400 mt-1">
                          Received: {formatDate(case_.date_received_from_carrier)}
                        </p>
                      </div>
                    </div>

                    {/* Collection */}
                    <div className="col-span-2">
                      {case_.collection_name ? (
                        <div className="flex items-center text-sm text-gray-600">
                          <FolderOpen className="h-4 w-4 mr-2 text-blue-500" />
                          <span className="truncate" title={case_.collection_name}>
                            {case_.collection_name}
                          </span>
                        </div>
                      ) : (
                        <span className="text-sm text-gray-400">No collection</span>
                      )}
                    </div>

                    {/* Status */}
                    <div className="col-span-2">
                      {getStatusBadge(case_.processing_status)}
                    </div>

                    {/* Processing Progress */}
                    <div className="col-span-3">
                      <div className="space-y-2">
                        <div className="flex items-center justify-between text-sm">
                          <span className="text-gray-600">
                            {case_.processing_status === 'in_progress' ? (
                              <span className="flex items-center">
                                <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse mr-2"></div>
                                Embedding Progress
                              </span>
                            ) : (
                              'Progress'
                            )}
                          </span>
                          <span className="font-medium text-gray-900">{case_.processing_progress}%</span>
                        </div>
                        <Progress
                          value={case_.processing_progress}
                          className={`h-2 ${case_.processing_status === 'in_progress' ? 'animate-pulse' : ''}`}
                        />
                        <div className="flex items-center justify-between text-xs text-gray-500">
                          <span>
                            {case_.processing_status === 'in_progress' ? (
                              `Embedding ${case_.processed_files} of ${case_.total_files} files...`
                            ) : (
                              `${case_.processed_files} of ${case_.total_files} embedded`
                            )}
                          </span>
                          {case_.error_files > 0 && (
                            <span className="text-red-600 font-medium">{case_.error_files} errors</span>
                          )}
                        </div>
                      </div>
                    </div>

                    {/* Files Count */}
                    <div className="col-span-1">
                      <div className="flex items-center text-sm text-gray-600">
                        <FileText className="h-4 w-4 mr-1" />
                        <span className="font-medium">{case_.total_files}</span>
                      </div>
                    </div>

                    {/* Actions */}
                    <div className="col-span-1">
                      <div className="flex items-center space-x-1">
                        <Button
                          size="sm"
                          variant="ghost"
                          className="h-8 w-8 p-0"
                          title="View Documents"
                          onClick={() => handleViewDocuments(case_)}
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button
                          size="sm"
                          variant="ghost"
                          className="h-8 w-8 p-0"
                          title="Case Details"
                          onClick={() => handleViewCaseDetails(case_)}
                        >
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </div>

                  {/* Mobile Layout */}
                  <div className="lg:hidden space-y-3">
                    {/* Header Row */}
                    <div className="flex items-start justify-between">
                      <div className="flex-1 min-w-0">
                        <h3 className="font-semibold text-gray-900 truncate" title={case_.case_name}>
                          {case_.case_name}
                        </h3>
                        <p className="text-sm text-gray-500 mt-1">
                          {case_.internal_case_number || 'No case number'}
                        </p>
                      </div>
                      <div className="ml-3 flex-shrink-0">
                        {getStatusBadge(case_.processing_status)}
                      </div>
                    </div>

                    {/* Details Row */}
                    <div className="flex items-center justify-between text-sm text-gray-600">
                      <div className="flex items-center">
                        <Calendar className="h-4 w-4 mr-1" />
                        <span>{formatDate(case_.date_received_from_carrier)}</span>
                      </div>
                      {case_.collection_name && (
                        <div className="flex items-center">
                          <FolderOpen className="h-4 w-4 mr-1 text-blue-500" />
                          <span className="truncate max-w-32" title={case_.collection_name}>
                            {case_.collection_name}
                          </span>
                        </div>
                      )}
                    </div>

                    {/* Progress Row */}
                    <div className="space-y-2">
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-gray-600">
                          {case_.processing_status === 'in_progress' ? (
                            <span className="flex items-center">
                              <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse mr-2"></div>
                              Embedding Progress
                            </span>
                          ) : (
                            'Processing Progress'
                          )}
                        </span>
                        <span className="font-medium text-gray-900">{case_.processing_progress}%</span>
                      </div>
                      <Progress
                        value={case_.processing_progress}
                        className={`h-2 ${case_.processing_status === 'in_progress' ? 'animate-pulse' : ''}`}
                      />
                      <div className="flex items-center justify-between text-xs text-gray-500">
                        <div className="flex items-center">
                          <FileText className="h-3 w-3 mr-1" />
                          <span>
                            {case_.processing_status === 'in_progress' ? (
                              `Embedding ${case_.processed_files} of ${case_.total_files} files...`
                            ) : (
                              `${case_.processed_files} of ${case_.total_files} files embedded`
                            )}
                          </span>
                        </div>
                        {case_.error_files > 0 && (
                          <span className="text-red-600 font-medium">{case_.error_files} errors</span>
                        )}
                      </div>
                    </div>

                    {/* Actions Row */}
                    <div className="flex items-center justify-end space-x-2 pt-2 border-t border-gray-100">
                      <Button
                        size="sm"
                        variant="outline"
                        className="text-xs"
                        onClick={() => handleViewDocuments(case_)}
                      >
                        <Eye className="h-3 w-3 mr-1" />
                        View
                      </Button>
                      <Button
                        size="sm"
                        variant="ghost"
                        className="text-xs"
                        title="Case Details"
                        onClick={() => handleViewCaseDetails(case_)}
                      >
                        <MoreHorizontal className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        ) : (
          // Grid View
          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
            {filteredCases.map((case_, index) => (
              <Card key={`${case_.id}-${index}`} className="bg-white/70 backdrop-blur-sm border-gray-200 hover:shadow-lg transition-all duration-200">
                <CardHeader className="pb-3">
                  <div className="flex items-start justify-between">
                    <div className="flex-1 min-w-0">
                      <CardTitle className="text-lg font-semibold text-gray-900 truncate">
                        {case_.case_name}
                      </CardTitle>
                      <p className="text-sm text-gray-500 mt-1">
                        {case_.internal_case_number || 'No case number'}
                      </p>
                    </div>
                    <div className="ml-2">
                      {getStatusBadge(case_.processing_status)}
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* Processing Progress */}
                  <div className="space-y-2">
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-gray-600">
                        {case_.processing_status === 'in_progress' ? (
                          <span className="flex items-center">
                            <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse mr-2"></div>
                            Embedding Progress
                          </span>
                        ) : (
                          'Document Processing'
                        )}
                      </span>
                      <span className="font-medium text-gray-900">{case_.processing_progress}%</span>
                    </div>
                    <Progress
                      value={case_.processing_progress}
                      className={`h-2 ${case_.processing_status === 'in_progress' ? 'animate-pulse' : ''}`}
                    />
                    <div className="flex items-center justify-between text-xs text-gray-500">
                      <span>
                        {case_.processing_status === 'in_progress' ? (
                          `Embedding ${case_.processed_files} of ${case_.total_files} files...`
                        ) : (
                          `${case_.processed_files} of ${case_.total_files} files embedded`
                        )}
                      </span>
                      {case_.error_files > 0 && (
                        <span className="text-red-600">{case_.error_files} errors</span>
                      )}
                    </div>
                  </div>

                  {/* Case Details */}
                  <div className="space-y-2 text-sm">
                    {case_.collection_name && (
                      <div className="flex items-center text-gray-600">
                        <FolderOpen className="h-4 w-4 mr-2" />
                        <span className="truncate">{case_.collection_name}</span>
                      </div>
                    )}
                    <div className="flex items-center text-gray-600">
                      <Calendar className="h-4 w-4 mr-2" />
                      <span>Received: {formatDate(case_.date_received_from_carrier)}</span>
                    </div>
                    <div className="flex items-center text-gray-600">
                      <FileText className="h-4 w-4 mr-2" />
                      <span>{case_.total_files} document{case_.total_files !== 1 ? 's' : ''}</span>
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="flex items-center justify-between pt-2 border-t border-gray-100">
                    <div className="flex items-center space-x-2">
                      <Button
                        size="sm"
                        variant="outline"
                        className="text-xs"
                        onClick={() => handleViewDocuments(case_)}
                      >
                        <Eye className="h-3 w-3 mr-1" />
                        View
                      </Button>
                    </div>
                    <Button
                      size="sm"
                      variant="ghost"
                      className="text-xs"
                      title="Case Details"
                      onClick={() => handleViewCaseDetails(case_)}
                    >
                      <MoreHorizontal className="h-3 w-3" />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>

      {/* Notifications */}
      <div className="fixed top-4 right-4 space-y-2 z-50">
        {notifications.map((notification) => (
          <div
            key={notification.id}
            className={cn(
              'p-4 rounded-lg shadow-lg border max-w-sm',
              {
                'bg-green-50 border-green-200 text-green-800': notification.type === 'success',
                'bg-red-50 border-red-200 text-red-800': notification.type === 'error',
                'bg-blue-50 border-blue-200 text-blue-800': notification.type === 'info',
                'bg-yellow-50 border-yellow-200 text-yellow-800': notification.type === 'warning',
              }
            )}
          >
            <div className="font-medium">{notification.title}</div>
            <div className="text-sm mt-1">{notification.message}</div>
          </div>
        ))}
      </div>

      {/* Case Details Modal */}
      {showCaseDetailsModal && selectedCaseForDetails && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-hidden">
            {/* Modal Header */}
            <div className="flex items-center justify-between p-6 border-b border-gray-200">
              <div>
                <h2 className="text-xl font-semibold text-gray-900">
                  Case Details
                </h2>
                <p className="text-sm text-gray-600 mt-1">
                  {selectedCaseForDetails.case_name}
                </p>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={handleCloseCaseDetailsModal}
                className="h-8 w-8 p-0"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>

            {/* Modal Content */}
            <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
              <div className="space-y-6">
                {/* Basic Information */}
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Basic Information</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="flex items-center space-x-3">
                      <Hash className="h-5 w-5 text-gray-400" />
                      <div>
                        <p className="text-sm font-medium text-gray-900">Case Number</p>
                        <p className="text-sm text-gray-600">
                          {selectedCaseForDetails.internal_case_number || 'Not assigned'}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-3">
                      <Calendar className="h-5 w-5 text-gray-400" />
                      <div>
                        <p className="text-sm font-medium text-gray-900">Date Received</p>
                        <p className="text-sm text-gray-600">
                          {formatDate(selectedCaseForDetails.date_received_from_carrier)}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-3">
                      <FolderOpen className="h-5 w-5 text-gray-400" />
                      <div>
                        <p className="text-sm font-medium text-gray-900">Collection</p>
                        <p className="text-sm text-gray-600">
                          {selectedCaseForDetails.collection_name || 'No collection assigned'}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-3">
                      <FileText className="h-5 w-5 text-gray-400" />
                      <div>
                        <p className="text-sm font-medium text-gray-900">Total Files</p>
                        <p className="text-sm text-gray-600">
                          {selectedCaseForDetails.total_files} document{selectedCaseForDetails.total_files !== 1 ? 's' : ''}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Processing Status */}
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Processing Status</h3>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium text-gray-900">Current Status</span>
                      {getStatusBadge(selectedCaseForDetails.processing_status)}
                    </div>
                    <div>
                      <div className="flex items-center justify-between text-sm mb-2">
                        <span className="text-gray-600">Processing Progress</span>
                        <span className="font-medium text-gray-900">{selectedCaseForDetails.processing_progress}%</span>
                      </div>
                      <Progress value={selectedCaseForDetails.processing_progress} className="h-2" />
                    </div>
                    <div className="grid grid-cols-3 gap-4 text-center">
                      <div className="bg-green-50 rounded-lg p-3">
                        <p className="text-lg font-semibold text-green-600">{selectedCaseForDetails.processed_files}</p>
                        <p className="text-xs text-green-600">Embedded</p>
                      </div>
                      <div className="bg-blue-50 rounded-lg p-3">
                        <p className="text-lg font-semibold text-blue-600">
                          {selectedCaseForDetails.total_files - selectedCaseForDetails.processed_files - selectedCaseForDetails.error_files}
                        </p>
                        <p className="text-xs text-blue-600">Pending</p>
                      </div>
                      <div className="bg-red-50 rounded-lg p-3">
                        <p className="text-lg font-semibold text-red-600">{selectedCaseForDetails.error_files}</p>
                        <p className="text-xs text-red-600">Errors</p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Actions */}
                <div className="flex items-center justify-end space-x-3 pt-4 border-t border-gray-200">
                  <Button
                    variant="outline"
                    onClick={() => {
                      handleCloseCaseDetailsModal();
                      handleViewDocuments(selectedCaseForDetails);
                    }}
                  >
                    <Eye className="h-4 w-4 mr-2" />
                    View Documents
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Document Modal */}
      {showDocumentModal && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
            {/* Modal Header */}
            <div className="flex items-center justify-between p-6 border-b border-gray-200">
              <div>
                <h2 className="text-xl font-semibold text-gray-900">
                  Case Documents
                </h2>
                {selectedCase && (
                  <p className="text-sm text-gray-600 mt-1">
                    {selectedCase.case_name} - {selectedCase.internal_case_number}
                  </p>
                )}
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={handleCloseDocumentModal}
                className="h-8 w-8 p-0"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>

            {/* Modal Content */}
            <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
              {loadingFiles ? (
                <div className="flex items-center justify-center py-12">
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                    <p className="text-gray-600 mt-2">Loading documents...</p>
                  </div>
                </div>
              ) : caseFiles.length === 0 ? (
                <div className="text-center py-12">
                  <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-600">No documents found for this case.</p>
                </div>
              ) : (
                <div className="space-y-3">
                  {caseFiles.map((file) => (
                    <div
                      key={file.id}
                      className="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50"
                    >
                      <div className="flex items-center space-x-3">
                        {getFileIcon(file.file_type)}
                        <div>
                          <p className="font-medium text-gray-900">{file.original_filename}</p>
                          <div className="flex items-center space-x-4 text-sm text-gray-500">
                            <span>{formatFileSize(file.file_size)}</span>
                            <span>{file.file_type}</span>
                            <span>Uploaded: {new Date(file.created_at).toLocaleDateString()}</span>
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Badge
                          variant={file.upload_status === 'completed' ? 'default' : 'secondary'}
                          className={cn(
                            file.upload_status === 'completed' && 'bg-green-100 text-green-800',
                            file.upload_status === 'error' && 'bg-red-100 text-red-800'
                          )}
                        >
                          {file.upload_status}
                        </Badge>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AllCasesPage;
