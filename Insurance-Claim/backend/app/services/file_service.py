from sqlalchemy.orm import Session
from typing import List
import uuid
import os
import shutil
from fastapi import UploadFile
import asyncio
from concurrent.futures import ThreadPoolExecutor

from app.models.case_file import CaseFile, CaseFileCreate, CaseFileResponse, FileUploadResponse
from app.models.case import Case


class FileService:
    @staticmethod
    async def upload_file(
        db: Session, 
        case_id: str, 
        file: UploadFile, 
        user_id: str
    ) -> FileUploadResponse:
        """Upload a file to a case"""
        # Validate case exists and belongs to user
        case = db.query(Case).filter(
            Case.id == case_id,
            Case.user_id == user_id
        ).first()
        
        if not case:
            raise ValueError("Case not found")
        
        # Validate file type
        if not file.content_type == "application/pdf":
            raise ValueError("Only PDF files are allowed")
        
        # Validate file size (50MB limit)
        if file.size > 50 * 1024 * 1024:
            raise ValueError("File size must be less than 50MB")


        print("✓  File Uploading Started")
        # Create upload directory if it doesn't exist
        upload_dir = f"uploads/cases/{case_id}"
        os.makedirs(upload_dir, exist_ok=True)
        
        # Generate unique filename
        file_id = str(uuid.uuid4())
        file_extension = os.path.splitext(file.filename)[1]
        filename = f"{file_id}{file_extension}"
        file_path = os.path.join(upload_dir, filename)
        
        try:
            # Save file
            with open(file_path, "wb") as buffer:
                shutil.copyfileobj(file.file, buffer)
            
            # Create file record in database
            db_file = CaseFile(
                id=file_id,
                filename=filename,
                original_filename=file.filename,
                file_size=file.size,
                file_type=file.content_type,
                file_path=file_path,
                upload_status="completed",
                case_id=case_id,
                user_id=user_id
            )
            
            db.add(db_file)
            db.commit()
            db.refresh(db_file)

            print("✓  File Uploading Completed")
            return FileUploadResponse(
                file_path=file_path,
                file_id=file_id,
                filename=file.filename,
                status="completed",
                message="File uploaded successfully"
            )

        except Exception as e:
            # Clean up file if database operation fails
            if os.path.exists(file_path):
                os.remove(file_path)
            raise e
    
    @staticmethod
    def get_case_files(db: Session, case_id: str, user_id: str) -> List[CaseFileResponse]:
        """Get all files for a case"""
        # Validate case exists and belongs to user
        case = db.query(Case).filter(
            Case.id == case_id,
            Case.user_id == user_id
        ).first()
        
        if not case:
            raise ValueError("Case not found")
        
        files = db.query(CaseFile).filter(CaseFile.case_id == case_id).all()
        return [CaseFileResponse(**file.__dict__) for file in files]

    @staticmethod
    def get_case_file(db: Session, case_id: str, file_id: str, user_id: str) -> CaseFile:
        """Get a specific file from a case"""
        # Validate case exists and belongs to user
        case = db.query(Case).filter(
            Case.id == case_id,
            Case.user_id == user_id
        ).first()

        if not case:
            raise ValueError("Case not found")

        # Get the specific file
        file = db.query(CaseFile).filter(
            CaseFile.id == file_id,
            CaseFile.case_id == case_id
        ).first()

        if not file:
            raise ValueError("File not found")

        return file

    @staticmethod
    def delete_case_file(db: Session, case_id: str, file_id: str, user_id: str) -> bool:
        """Delete a file from a case"""
        # Validate case exists and belongs to user
        case = db.query(Case).filter(
            Case.id == case_id,
            Case.user_id == user_id
        ).first()
        
        if not case:
            raise ValueError("Case not found")
        
        # Validate file exists and belongs to case
        file = db.query(CaseFile).filter(
            CaseFile.id == file_id,
            CaseFile.case_id == case_id
        ).first()
        
        if not file:
            raise ValueError("File not found")
        
        # Delete file from storage
        try:
            if os.path.exists(file.file_path):
                os.remove(file.file_path)
        except Exception as e:
            print(f"Error deleting file {file.file_path}: {e}")
        
        # Delete file record
        db.delete(file)
        db.commit()
        
        return True
    
    @staticmethod
    def get_file_path(db: Session, file_id: str, user_id: str) -> str:
        """Get the file path for a specific file"""
        file = db.query(CaseFile).filter(
            CaseFile.id == file_id,
            CaseFile.user_id == user_id
        ).first()
        
        if not file:
            raise ValueError("File not found")
        
        if not os.path.exists(file.file_path):
            raise ValueError("File not found on disk")
        
        return file.file_path

    @staticmethod
    async def upload_files_batch(
        db: Session,
        case_id: str,
        files: List[UploadFile],
        user_id: str
    ) -> List[FileUploadResponse]:
        """Upload multiple files to a case without processing embeddings"""
        # Validate case exists and belongs to user
        case = db.query(Case).filter(
            Case.id == case_id,
            Case.user_id == user_id
        ).first()

        if not case:
            raise ValueError("Case not found")

        results = []

        for file in files:
            try:
                # Validate file type
                if not file.content_type == "application/pdf":
                    results.append(FileUploadResponse(
                        file_path="",
                        file_id="",
                        filename=file.filename,
                        status="error",
                        message="Only PDF files are allowed"
                    ))
                    continue

                # Validate file size (50MB limit)
                if file.size > 50 * 1024 * 1024:
                    results.append(FileUploadResponse(
                        file_path="",
                        file_id="",
                        filename=file.filename,
                        status="error",
                        message="File size must be less than 50MB"
                    ))
                    continue

                print(f"✓  File Uploading Started: {file.filename}")
                # Create upload directory if it doesn't exist
                upload_dir = f"uploads/cases/{case_id}"
                os.makedirs(upload_dir, exist_ok=True)

                # Generate unique filename
                file_id = str(uuid.uuid4())
                file_extension = os.path.splitext(file.filename)[1]
                filename = f"{file_id}{file_extension}"
                file_path = os.path.join(upload_dir, filename)

                # Save file
                with open(file_path, "wb") as buffer:
                    shutil.copyfileobj(file.file, buffer)

                # Create file record in database
                db_file = CaseFile(
                    id=file_id,
                    filename=filename,
                    original_filename=file.filename,
                    file_size=file.size,
                    file_type=file.content_type,
                    file_path=file_path,
                    upload_status="uploaded",  # Set to "uploaded" initially, will be updated to "embedding" then "completed"
                    case_id=case_id,
                    user_id=user_id
                )

                db.add(db_file)
                db.commit()
                db.refresh(db_file)

                print(f"✓  File Uploading Completed: {file.filename}")
                results.append(FileUploadResponse(
                    file_path=file_path,
                    file_id=file_id,
                    filename=file.filename,
                    status="completed",
                    message="File uploaded successfully"
                ))

            except Exception as e:
                # Clean up file if it exists and database operation fails
                if 'file_path' in locals() and os.path.exists(file_path):
                    os.remove(file_path)

                results.append(FileUploadResponse(
                    file_path="",
                    file_id="",
                    filename=file.filename,
                    status="error",
                    message=f"Upload failed: {str(e)}"
                ))

        return results