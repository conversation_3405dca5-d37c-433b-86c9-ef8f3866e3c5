from pydantic import BaseModel
from datetime import datetime
from typing import Optional
from sqlalchemy import Column, String, DateTime, ForeignKey, Integer, Text
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from app.core.database import Base

# SQLAlchemy ORM Model
class CaseFile(Base):
    __tablename__ = "case_files"

    id = Column(String, primary_key=True, index=True)
    filename = Column(String, nullable=False)
    original_filename = Column(String, nullable=False)
    file_size = Column(Integer, nullable=False)  # Size in bytes
    file_type = Column(String, nullable=False)  # MIME type
    file_path = Column(String, nullable=False)  # Path to stored file
    upload_status = Column(String, default="pending")  # pending, uploading, uploaded, embedding, completed, error
    error_message = Column(Text, nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Foreign key to case
    case_id = Column(String, ForeignKey("cases.id"), nullable=False)
    
    # Foreign key to user who uploaded the file
    user_id = Column(String, ForeignKey("users.id"), nullable=False)
    
    # Relationships
    case = relationship("Case", back_populates="files")
    user = relationship("User", back_populates="uploaded_files")

# Pydantic Models for API
class CaseFileBase(BaseModel):
    filename: str
    original_filename: str
    file_size: int
    file_type: str
    upload_status: str = "pending"

class CaseFileCreate(CaseFileBase):
    case_id: str

class CaseFileUpdate(BaseModel):
    upload_status: Optional[str] = None
    error_message: Optional[str] = None

class CaseFileResponse(CaseFileBase):
    id: str
    case_id: str
    user_id: str
    file_path: str
    error_message: Optional[str] = None
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True

class FileUploadResponse(BaseModel):
    file_path: str
    file_id: str
    filename: str
    status: str
    message: str 