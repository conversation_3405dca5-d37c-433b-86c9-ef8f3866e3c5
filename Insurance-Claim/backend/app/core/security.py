from passlib.context import Crypt<PERSON>ontext
from datetime import datetime, timedelta
import jwt
import os
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
expire_min = int(os.getenv('JWT_EXPIRE_MINUTES', '30'))  # Convert to int with default
secret_key = os.getenv('JWT_SECRET_KEY')
algorithm = os.getenv('JWT_ALGORITHM')

def hash_password(password: str) -> str:
    return pwd_context.hash(password)

def verify_password(plain_password: str, hashed_password: str) -> bool:
    return pwd_context.verify(plain_password, hashed_password)

def create_access_token(data: dict, expires_delta: timedelta = None):
    to_encode = data.copy()
    expire = datetime.utcnow() + (expires_delta or timedelta(minutes=expire_min))
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, secret_key, algorithm=algorithm)
    return encoded_jwt

def decode_access_token(token: str):
    try:
        payload = jwt.decode(token, secret_key, algorithms=[algorithm])
        return payload
    except jwt.InvalidTokenError:
        return None
